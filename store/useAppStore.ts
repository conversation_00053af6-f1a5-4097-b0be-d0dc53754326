import { create } from 'zustand';
import { Message } from '@/types';

interface Document {
  name: string;
  size?: number;
  uri: string;
  mimeType?: string;
}

interface AppState {
  selectedDocument: Document | null;
  messages: Message[];
  recentDocuments: Document[];
  setSelectedDocument: (document: Document | null) => void;
  addMessage: (message: Message) => void;
  clearMessages: () => void;
  addToRecentDocuments: (document: Document) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  selectedDocument: null,
  messages: [],
  recentDocuments: [],
  
  setSelectedDocument: (document) => {
    set({ selectedDocument: document, messages: [] });
    if (document) {
      get().addToRecentDocuments(document);
    }
  },
  
  addMessage: (message) => set((state) => ({ 
    messages: [...state.messages, message] 
  })),
  
  clearMessages: () => set({ messages: [] }),
  
  addToRecentDocuments: (document) => set((state) => {
    const existing = state.recentDocuments.find(doc => doc.uri === document.uri);
    if (existing) return state;
    
    const newRecent = [document, ...state.recentDocuments.slice(0, 4)];
    return { recentDocuments: newRecent };
  }),
}));
/**
 * Simple test script to verify the sendMessage function works
 * Run with: node test-sendmessage.js
 */

const axios = require('axios');

// Mock the sendMessage function for testing
class MockApiService {
  constructor() {
    this.baseURL = 'http://127.0.0.1:11434';
    this.timeout = 30000;
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.baseURL}/api/version`, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  async generate(request) {
    try {
      const response = await axios.post(`${this.baseURL}/api/generate`, request, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: this.timeout
      });
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.data?.error || error.message}`);
      } else if (error.request) {
        throw new Error('Network error: Unable to reach the server');
      } else {
        throw new Error(error.message);
      }
    }
  }

  async sendMessage(message, model = 'deepseek-r1:14b', systemPrompt, performHealthCheck = true) {
    try {
      // Validate input
      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      if (!model || model.trim().length === 0) {
        throw new Error('Model name cannot be empty');
      }

      // Optional health check
      if (performHealthCheck) {
        const isHealthy = await this.healthCheck();
        if (!isHealthy) {
          throw new Error('AI service is currently unavailable');
        }
      }

      // Prepare the request
      const request = {
        model: model.trim(),
        prompt: message.trim(),
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
        },
      };

      if (systemPrompt) {
        request.system = systemPrompt.trim();
      }

      // Make the request
      const response = await this.generate(request);

      // Validate response
      if (!response.response || response.response.trim().length === 0) {
        throw new Error('Received empty response from AI');
      }

      return response.response.trim();

    } catch (error) {
      // Enhanced error handling with user-friendly messages
      console.error('[sendMessage] Error:', error.message);

      if (error.message.includes('Network error')) {
        throw new Error('Unable to connect to the AI service. Please check your internet connection.');
      } else if (error.message.includes('HTTP 404')) {
        throw new Error(`The AI model "${model}" is not available. Please check the model name.`);
      } else if (error.message.includes('HTTP 429')) {
        throw new Error('Too many requests. Please wait a moment and try again.');
      } else if (error.message.includes('HTTP 500')) {
        throw new Error('The AI service is experiencing technical difficulties. Please try again.');
      } else if (error.message.includes('timeout')) {
        throw new Error('Request timed out. The AI service may be busy. Please try again.');
      } else if (error.message.includes('unavailable')) {
        throw new Error('The AI service is currently unavailable. Please try again later.');
      } else if (error.message.includes('empty')) {
        throw new Error('The AI did not provide a response. Please try rephrasing your message.');
      } else {
        throw new Error('An unexpected error occurred while processing your message. Please try again.');
      }
    }
  }
}

// Test functions
async function testBasicSendMessage() {
  console.log('🧪 Testing basic sendMessage functionality...');
  
  const apiService = new MockApiService();
  
  try {
    const response = await apiService.sendMessage(
      'Say "Hello, World!" and nothing else.',
      'deepseek-r1:14b',
      'You are a helpful assistant. Follow instructions exactly.'
    );
    
    console.log('✅ sendMessage succeeded');
    console.log('📋 Response:', response.substring(0, 100) + (response.length > 100 ? '...' : ''));
    return true;
  } catch (error) {
    console.error('❌ sendMessage failed:', error.message);
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🔧 Testing error handling...');
  
  const apiService = new MockApiService();
  
  // Test empty message
  try {
    await apiService.sendMessage('');
    console.log('⚠️ Expected error for empty message but succeeded');
  } catch (error) {
    console.log('✅ Empty message error handled correctly:', error.message);
  }
  
  // Test non-existent model
  try {
    await apiService.sendMessage('Hello', 'non-existent-model', undefined, false);
    console.log('⚠️ Expected error for non-existent model but succeeded');
  } catch (error) {
    console.log('✅ Non-existent model error handled correctly:', error.message);
  }
}

async function testHealthCheck() {
  console.log('\n🏥 Testing health check integration...');
  
  const apiService = new MockApiService();
  
  try {
    // Test with health check enabled
    const response = await apiService.sendMessage(
      'Quick test',
      'deepseek-r1:14b',
      undefined,
      true // Enable health check
    );
    console.log('✅ Health check integration works');
    return true;
  } catch (error) {
    if (error.message.includes('unavailable')) {
      console.log('✅ Health check correctly detected unavailable service');
      return true;
    } else {
      console.error('❌ Health check integration failed:', error.message);
      return false;
    }
  }
}

async function testDifferentModels() {
  console.log('\n🤖 Testing different model configurations...');
  
  const apiService = new MockApiService();
  
  const testCases = [
    { model: 'deepseek-r1:14b', message: 'Hello' },
    { model: 'deepseek-r1:14b', message: 'What is AI?', system: 'Be concise.' },
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`📤 Testing model: ${testCase.model}`);
      const response = await apiService.sendMessage(
        testCase.message,
        testCase.model,
        testCase.system,
        false // Skip health check for speed
      );
      console.log(`✅ Model ${testCase.model} works`);
    } catch (error) {
      console.log(`❌ Model ${testCase.model} failed:`, error.message);
    }
  }
}

async function runTests() {
  console.log('🚀 Starting sendMessage function tests...\n');
  
  const results = [];
  
  results.push(await testBasicSendMessage());
  await testErrorHandling();
  results.push(await testHealthCheck());
  await testDifferentModels();
  
  const successCount = results.filter(Boolean).length;
  const totalTests = results.length;
  
  console.log(`\n🏁 Tests completed: ${successCount}/${totalTests} passed`);
  
  if (successCount === totalTests) {
    console.log('✅ All tests passed! The sendMessage function is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the API service and model availability.');
  }
}

// Run the tests
runTests().catch(console.error);

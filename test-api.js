/**
 * Simple test script to verify the API service works
 * Run with: node test-api.js
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://127.0.0.1:11434';
const TEST_MODEL = 'deepseek-r1:14b';
const TEST_PROMPT = 'Why is the sky blue?';

async function testBasicConnection() {
  console.log('🔍 Testing basic connection to Ollama API...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/version`, {
      timeout: 5000
    });
    
    console.log('✅ API is reachable');
    console.log('📋 Version info:', response.data);
    return true;
  } catch (error) {
    console.error('❌ API connection failed:', error.message);
    return false;
  }
}

async function testGenerateEndpoint() {
  console.log('\n🧪 Testing /api/generate endpoint...');
  
  try {
    const requestData = {
      model: TEST_MODEL,
      prompt: TEST_PROMPT,
      stream: true
    };
    
    console.log('📤 Sending request:', requestData);
    
    const response = await axios.post(`${API_BASE_URL}/api/generate`, requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('✅ Generate request successful');
    console.log('📋 Response preview:', {
      model: response.data.model,
      responseLength: response.data.response?.length || 0,
      done: response.data.done,
      totalDuration: response.data.total_duration
    });
    
    if (response.data.response) {
      console.log('💬 AI Response (first 200 chars):', 
        response.data.response.substring(0, 200) + '...'
      );
    }
    
    return true;
  } catch (error) {
    console.error('❌ Generate request failed:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      data: error.response?.data
    });
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🔧 Testing error handling...');
  
  try {
    // Test with non-existent model
    await axios.post(`${API_BASE_URL}/api/generate`, {
      model: 'non-existent-model',
      prompt: 'test'
    }, {
      timeout: 5000
    });
    
    console.log('⚠️ Expected error but request succeeded');
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Error handling works correctly (404 for non-existent model)');
    } else {
      console.log('📋 Got error:', {
        status: error.response?.status,
        message: error.response?.data?.error || error.message
      });
    }
  }
}

async function runTests() {
  console.log('🚀 Starting Ollama API tests...\n');
  
  const connectionOk = await testBasicConnection();
  
  if (connectionOk) {
    await testGenerateEndpoint();
    await testErrorHandling();
  } else {
    console.log('\n❌ Cannot proceed with tests - API is not reachable');
    console.log('💡 Make sure Ollama is running: ollama serve');
    console.log('💡 And the model is available: ollama pull deepseek-r1:14b');
  }
  
  console.log('\n🏁 Tests completed');
}

// Run the tests
runTests().catch(console.error);

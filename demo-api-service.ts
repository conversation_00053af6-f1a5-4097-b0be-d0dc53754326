/**
 * Demonstration of the production-ready Axios API service
 * This file shows how to use the new API service in your React Native/Expo app
 */

import { apiService, ApiServiceError } from './services/api';
import { defaultChatService } from './services/api-examples';

/**
 * Example React Native component usage
 */
export const ExampleUsage = {
  
  /**
   * Basic usage - equivalent to your curl command
   */
  async basicExample() {
    try {
      // This is equivalent to:
      // curl http://127.0.0.1:11434/api/generate -d '{"model": "deepseek-r1:14b", "prompt": "Why is the sky blue?"}'
      
      const response = await apiService.sendPrompt(
        'deepseek-r1:14b',
        'Why is the sky blue?'
      );
      
      console.log('AI Response:', response);
      return response;
    } catch (error) {
      if (error instanceof ApiServiceError) {
        console.error('API Error:', error.message);
        // Handle specific error types
        switch (error.code) {
          case 'NETWORK_ERROR':
            throw new Error('Please check your internet connection');
          case 'HTTP_ERROR':
            if (error.status === 404) {
              throw new Error('Model not found. Please check the model name.');
            }
            break;
        }
      }
      throw error;
    }
  },

  /**
   * Advanced usage with full configuration
   */
  async advancedExample() {
    try {
      const response = await apiService.generate({
        model: 'deepseek-r1:14b',
        prompt: 'Explain quantum computing in simple terms',
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40,
          num_predict: 500,
        },
        system: 'You are a helpful AI assistant that explains complex topics clearly.',
      });
      
      return {
        text: response.response,
        metadata: {
          model: response.model,
          duration: response.total_duration,
          tokenCount: response.eval_count,
        }
      };
    } catch (error) {
      console.error('Advanced generation failed:', error);
      throw error;
    }
  },

  /**
   * Health check before making requests
   */
  async withHealthCheck() {
    // Always check health before important operations
    const isHealthy = await apiService.healthCheck();
    
    if (!isHealthy) {
      throw new Error('API service is currently unavailable');
    }
    
    return await this.basicExample();
  },

  /**
   * Using the chat service wrapper
   */
  async chatServiceExample() {
    try {
      const response = await defaultChatService.sendMessage(
        'What is machine learning?',
        'You are an expert in AI and machine learning.'
      );
      
      return response;
    } catch (error) {
      console.error('Chat service error:', error);
      throw new Error('Failed to get AI response. Please try again.');
    }
  },

  /**
   * Batch processing multiple prompts
   */
  async batchProcessing() {
    const prompts = [
      'What is artificial intelligence?',
      'Explain neural networks',
      'What is deep learning?',
    ];

    const results = await Promise.allSettled(
      prompts.map(prompt => 
        apiService.sendPrompt('deepseek-r1:14b', prompt)
      )
    );

    return results.map((result, index) => ({
      prompt: prompts[index],
      success: result.status === 'fulfilled',
      response: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null,
    }));
  },

  /**
   * Configuration management
   */
  manageConfiguration() {
    // Get current configuration
    const config = apiService.getConfig();
    console.log('Current config:', config);

    // Update retry settings for production
    apiService.updateRetryConfig({
      attempts: 2, // Fewer retries for faster failure detection
      delay: 2000, // Longer delay between retries
      backoff: true, // Use exponential backoff
    });

    console.log('Updated config:', apiService.getConfig());
  },
};

/**
 * React Native component example
 */
export const ChatComponent = {
  async sendMessage(message: string): Promise<string> {
    try {
      // Health check first
      const isHealthy = await apiService.healthCheck();
      if (!isHealthy) {
        throw new Error('AI service is currently unavailable. Please try again later.');
      }

      // Send the message
      const response = await apiService.sendPrompt('deepseek-r1:14b', message, {
        options: {
          temperature: 0.7,
          top_p: 0.9,
        },
        system: 'You are a helpful assistant. Keep responses concise and helpful.',
      });

      return response;
    } catch (error) {
      if (error instanceof ApiServiceError) {
        // Log for debugging
        console.error('[ChatComponent] API Error:', {
          message: error.message,
          status: error.status,
          code: error.code,
        });

        // Return user-friendly error messages
        switch (error.code) {
          case 'NETWORK_ERROR':
            return 'I\'m having trouble connecting. Please check your internet connection.';
          case 'HTTP_ERROR':
            if (error.status === 429) {
              return 'I\'m receiving too many requests. Please wait a moment and try again.';
            }
            return 'I\'m experiencing technical difficulties. Please try again.';
          default:
            return 'Something went wrong. Please try again.';
        }
      }
      
      return 'An unexpected error occurred. Please try again.';
    }
  },
};

/**
 * Production deployment checklist
 */
export const ProductionChecklist = {
  /**
   * Verify production configuration
   */
  async verifyProductionSetup(): Promise<boolean> {
    const config = apiService.getConfig();
    
    console.log('🔍 Verifying production setup...');
    
    // Check if using production URL
    const isProduction = !config.baseURL?.includes('localhost') && 
                        !config.baseURL?.includes('127.0.0.1');
    
    if (!isProduction) {
      console.warn('⚠️ Still using localhost URL in production');
    }
    
    // Check timeout settings
    const hasReasonableTimeout = config.timeout && config.timeout >= 30000;
    if (!hasReasonableTimeout) {
      console.warn('⚠️ Timeout might be too short for production');
    }
    
    // Test API connectivity
    const isHealthy = await apiService.healthCheck();
    if (!isHealthy) {
      console.error('❌ API health check failed');
      return false;
    }
    
    console.log('✅ Production setup verified');
    return true;
  },

  /**
   * Performance monitoring
   */
  async performanceTest(): Promise<void> {
    console.log('🚀 Running performance test...');
    
    const startTime = Date.now();
    
    try {
      await apiService.sendPrompt('deepseek-r1:14b', 'Hello');
      const duration = Date.now() - startTime;
      
      console.log(`✅ Response time: ${duration}ms`);
      
      if (duration > 10000) {
        console.warn('⚠️ Response time is slow (>10s)');
      }
    } catch (error) {
      console.error('❌ Performance test failed:', error);
    }
  },
};

// Export for use in your app
export { apiService, ApiServiceError } from './services/api';
export { defaultChatService } from './services/api-examples';

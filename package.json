{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 npx expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "axios": "^1.10.0", "expo": "^53.0.0", "expo-blur": "^14.1.5", "expo-camera": "^16.1.8", "expo-clipboard": "^7.1.4", "expo-constants": "^17.1.6", "expo-document-picker": "^13.1.6", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "immer": "^10.1.1", "lucide-react-native": "^0.475.0", "openai": "^5.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Copy, ThumbsUp, ThumbsDown, User, Bot } from 'lucide-react-native';
import Animated, { FadeInRight, FadeInLeft } from 'react-native-reanimated';
import * as Clipboard from 'expo-clipboard';
import { Message } from '@/types';

interface ChatMessageProps {
  message: Message;
}

export function ChatMessage({ message }: ChatMessageProps) {
  const [liked, setLiked] = useState<boolean | null>(null);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleCopy = async () => {
    await Clipboard.setStringAsync(message.text);
    Alert.alert('Copied', 'Message copied to clipboard');
  };

  const handleLike = (isLike: boolean) => {
    setLiked(isLike);
  };

  return (
    <Animated.View 
      entering={message.isUser ? FadeInRight.delay(100) : FadeInLeft.delay(100)}
      style={[
        styles.container,
        message.isUser ? styles.userContainer : styles.aiContainer,
      ]}
    >
      {/* Avatar */}
      {!message.isUser && (
        <View style={styles.avatar}>
          <LinearGradient
            colors={['#6366F1', '#8B5CF6']}
            style={styles.avatarGradient}
          >
            <Bot size={16} color="#FFFFFF" strokeWidth={2.5} />
          </LinearGradient>
        </View>
      )}

      <View style={[
        styles.bubble,
        message.isUser ? styles.userBubble : styles.aiBubble,
        message.isError && styles.errorBubble,
      ]}>
        {message.isUser ? (
          <LinearGradient
            colors={['#6366F1', '#8B5CF6']}
            style={styles.userBubbleGradient}
          >
            <Text style={styles.userText}>{message.text}</Text>
            <Text style={styles.userTimestamp}>
              {formatTime(message.timestamp)}
            </Text>
          </LinearGradient>
        ) : (
          <View style={styles.aiBubbleContent}>
            <Text style={[
              styles.aiText,
              message.isError && styles.errorText,
            ]}>
              {message.text}
            </Text>
            
            <View style={styles.messageFooter}>
              <Text style={styles.aiTimestamp}>
                {formatTime(message.timestamp)}
              </Text>
              
              {!message.isError && (
                <View style={styles.messageActions}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={handleCopy}
                    activeOpacity={0.7}
                  >
                    <Copy size={14} color="#94A3B8" strokeWidth={2.5} />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      liked === true && styles.actionButtonActive
                    ]}
                    onPress={() => handleLike(true)}
                    activeOpacity={0.7}
                  >
                    <ThumbsUp 
                      size={14} 
                      color={liked === true ? '#10B981' : '#94A3B8'} 
                      strokeWidth={2.5}
                      fill={liked === true ? '#10B981' : 'transparent'}
                    />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      liked === false && styles.actionButtonActive
                    ]}
                    onPress={() => handleLike(false)}
                    activeOpacity={0.7}
                  >
                    <ThumbsDown 
                      size={14} 
                      color={liked === false ? '#EF4444' : '#94A3B8'} 
                      strokeWidth={2.5}
                      fill={liked === false ? '#EF4444' : 'transparent'}
                    />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        )}
      </View>

      {/* User Avatar */}
      {message.isUser && (
        <View style={styles.avatar}>
          <LinearGradient
            colors={['#10B981', '#059669']}
            style={styles.avatarGradient}
          >
            <User size={16} color="#FFFFFF" strokeWidth={2.5} />
          </LinearGradient>
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-end',
  },
  userContainer: {
    justifyContent: 'flex-end',
  },
  aiContainer: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 8,
  },
  avatarGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bubble: {
    maxWidth: '75%',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userBubble: {
    borderBottomRightRadius: 6,
    overflow: 'hidden',
  },
  aiBubble: {
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  errorBubble: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
  },
  userBubbleGradient: {
    padding: 16,
  },
  aiBubbleContent: {
    padding: 16,
  },
  userText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    lineHeight: 22,
    marginBottom: 8,
  },
  aiText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1E293B',
    lineHeight: 22,
    marginBottom: 12,
  },
  errorText: {
    color: '#DC2626',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userTimestamp: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  aiTimestamp: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#94A3B8',
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F8FAFC',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  actionButtonActive: {
    backgroundColor: '#EBF4FF',
    borderColor: '#BFDBFE',
  },
});
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FileText, X, Upload, CircleCheck as CheckCircle, Calendar } from 'lucide-react-native';
import Animated, { FadeInDown, ZoomIn } from 'react-native-reanimated';

interface DocumentUploadCardProps {
  document: {
    name: string;
    size?: number;
    uri: string;
    mimeType?: string;
  };
  onRemove: () => void;
  onReplace: () => void;
}

export function DocumentUploadCard({ document, onRemove, onReplace }: DocumentUploadCardProps) {
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const mb = bytes / (1024 * 1024);
    return mb > 1 ? `${mb.toFixed(1)} MB` : `${(bytes / 1024).toFixed(1)} KB`;
  };

  const handleRemove = () => {
    Alert.alert(
      'Remove Document',
      'Are you sure you want to remove this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: onRemove }
      ]
    );
  };

  return (
    <Animated.View 
      entering={FadeInDown.delay(200)}
      style={styles.container}
    >
      <LinearGradient
        colors={['#FFFFFF', '#F8FAFC']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <Animated.View entering={ZoomIn.delay(400)} style={styles.iconContainer}>
            <LinearGradient
              colors={['#10B981', '#059669']}
              style={styles.iconGradient}
            >
              <FileText size={24} color="#FFFFFF" strokeWidth={2.5} />
            </LinearGradient>
            <View style={styles.successBadge}>
              <CheckCircle size={16} color="#10B981" strokeWidth={2.5} />
            </View>
          </Animated.View>
          
          <TouchableOpacity onPress={handleRemove} style={styles.removeButton}>
            <X size={18} color="#EF4444" strokeWidth={2.5} />
          </TouchableOpacity>
        </View>
        
        {/* Content */}
        <View style={styles.content}>
          <Text style={styles.fileName} numberOfLines={2}>
            {document.name}
          </Text>
          <View style={styles.fileDetails}>
            <Text style={styles.fileSize}>
              {formatFileSize(document.size)}
            </Text>
            <View style={styles.separator} />
            <View style={styles.uploadedInfo}>
              <Calendar size={12} color="#10B981" strokeWidth={2.5} />
              <Text style={styles.uploadedText}>Just now</Text>
            </View>
          </View>
          
          <View style={styles.statusContainer}>
            <View style={styles.statusIndicator}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>Ready for analysis</Text>
            </View>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actions}>
          <TouchableOpacity style={styles.replaceButton} onPress={onReplace}>
            <Upload size={16} color="#6366F1" strokeWidth={2.5} />
            <Text style={styles.replaceText}>Replace Document</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#10B981',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  gradient: {
    borderWidth: 2,
    borderRadius: 16,
    overflow: 'hidden',
    borderColor: '#10B981',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingBottom: 0,
  },
  iconContainer: {
    position: 'relative',
  },
  iconGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#10B981',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  successBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  removeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  content: {
    padding: 20,
    paddingTop: 16,
  },
  fileName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    marginBottom: 8,
    lineHeight: 24,
  },
  fileDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  fileSize: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#64748B',
  },
  separator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#CBD5E1',
    marginHorizontal: 8,
  },
  uploadedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploadedText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#10B981',
    marginLeft: 4,
  },
  statusContainer: {
    marginTop: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#10B981',
  },
  actions: {
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
    padding: 20,
    paddingTop: 16,
  },
  replaceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EBF4FF',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#BFDBFE',
  },
  replaceText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6366F1',
    marginLeft: 8,
  },
});
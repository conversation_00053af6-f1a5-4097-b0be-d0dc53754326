/**
 * Example React Native component demonstrating how to use the sendMessage function
 * This shows practical integration with the production-ready API service
 */

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StyleSheet } from 'react-native';
import { sendMessage, apiService } from '../services/api';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isError?: boolean;
}

export const ChatComponent: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('deepseek-r1:14b');

  const addMessage = (text: string, isUser: boolean, isError: boolean = false) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      isUser,
      timestamp: new Date(),
      isError,
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage = inputText.trim();
    setInputText('');
    addMessage(userMessage, true);
    setIsLoading(true);

    try {
      // Using the sendMessage function with default settings
      const response = await sendMessage(
        userMessage,
        selectedModel,
        'You are a helpful AI assistant. Provide clear and concise responses.',
        true // Perform health check
      );

      addMessage(response, false);
    } catch (error) {
      console.error('Chat error:', error);
      addMessage(
        error instanceof Error ? error.message : 'An unexpected error occurred',
        false,
        true
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleHealthCheck = async () => {
    try {
      const isHealthy = await apiService.healthCheck();
      Alert.alert(
        'Health Check',
        isHealthy ? 'API is healthy and ready!' : 'API is not responding',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Health Check Failed', 'Unable to check API status', [{ text: 'OK' }]);
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>AI Chat</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.healthButton} onPress={handleHealthCheck}>
            <Text style={styles.buttonText}>Health Check</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.clearButton} onPress={clearChat}>
            <Text style={styles.buttonText}>Clear</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Model Selection */}
      <View style={styles.modelSelector}>
        <Text style={styles.modelLabel}>Model: {selectedModel}</Text>
      </View>

      {/* Messages */}
      <ScrollView style={styles.messagesContainer}>
        {messages.map((message) => (
          <View
            key={message.id}
            style={[
              styles.messageContainer,
              message.isUser ? styles.userMessage : styles.aiMessage,
              message.isError && styles.errorMessage,
            ]}
          >
            <Text style={[
              styles.messageText,
              message.isUser ? styles.userMessageText : styles.aiMessageText,
              message.isError && styles.errorMessageText,
            ]}>
              {message.text}
            </Text>
            <Text style={styles.timestamp}>
              {message.timestamp.toLocaleTimeString()}
            </Text>
          </View>
        ))}
        {isLoading && (
          <View style={[styles.messageContainer, styles.aiMessage]}>
            <Text style={styles.loadingText}>AI is thinking...</Text>
          </View>
        )}
      </ScrollView>

      {/* Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Type your message..."
          multiline
          maxLength={1000}
          editable={!isLoading}
        />
        <TouchableOpacity
          style={[styles.sendButton, (!inputText.trim() || isLoading) && styles.sendButtonDisabled]}
          onPress={handleSendMessage}
          disabled={!inputText.trim() || isLoading}
        >
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Alternative usage examples for different scenarios
export const ChatExamples = {
  
  /**
   * Basic usage - simplest form
   */
  async basicUsage() {
    try {
      const response = await sendMessage('Hello, how are you?');
      console.log('AI Response:', response);
      return response;
    } catch (error) {
      console.error('Error:', error.message);
      throw error;
    }
  },

  /**
   * With custom model and system prompt
   */
  async customizedUsage() {
    try {
      const response = await sendMessage(
        'Explain quantum computing',
        'deepseek-r1:14b',
        'You are a physics professor. Explain complex topics in simple terms.'
      );
      return response;
    } catch (error) {
      console.error('Error:', error.message);
      throw error;
    }
  },

  /**
   * Skip health check for faster response (use when you know API is healthy)
   */
  async fastUsage() {
    try {
      const response = await sendMessage(
        'Quick question: what is 2+2?',
        'deepseek-r1:14b',
        undefined,
        false // Skip health check
      );
      return response;
    } catch (error) {
      console.error('Error:', error.message);
      throw error;
    }
  },

  /**
   * Error handling example
   */
  async errorHandlingExample() {
    try {
      const response = await sendMessage('Hello', 'non-existent-model');
      return response;
    } catch (error) {
      // The sendMessage function provides user-friendly error messages
      console.log('User-friendly error:', error.message);
      
      // You can show this error message directly to users
      Alert.alert('Chat Error', error.message);
    }
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  healthButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  clearButton: {
    backgroundColor: '#f44336',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  modelSelector: {
    padding: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modelLabel: {
    fontSize: 14,
    color: '#666',
  },
  messagesContainer: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#2196F3',
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  errorMessage: {
    backgroundColor: '#ffebee',
    borderColor: '#f44336',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: '#fff',
  },
  aiMessageText: {
    color: '#333',
  },
  errorMessageText: {
    color: '#d32f2f',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default ChatComponent;

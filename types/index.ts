export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isError?: boolean;
}

export interface Document {
  name: string;
  size?: number;
  uri: string;
  mimeType?: string;
}

export interface ChatResponse {
  response: string;
  status: string;
}

// API Configuration types
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

// Environment variables interface
export interface EnvironmentConfig {
  EXPO_PUBLIC_API_BASE_URL?: string;
  EXPO_PUBLIC_API_TIMEOUT?: string;
  EXPO_PUBLIC_API_RETRY_ATTEMPTS?: string;
  EXPO_PUBLIC_API_RETRY_DELAY?: string;
}
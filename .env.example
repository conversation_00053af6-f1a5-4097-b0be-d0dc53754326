# API Configuration
# Base URL for the Ollama API
EXPO_PUBLIC_API_BASE_URL=http://127.0.0.1:11434

# Request timeout in milliseconds (default: 30000 = 30 seconds)
EXPO_PUBLIC_API_TIMEOUT=30000

# Number of retry attempts for failed requests (default: 3)
EXPO_PUBLIC_API_RETRY_ATTEMPTS=3

# Base delay between retries in milliseconds (default: 1000 = 1 second)
# Note: With exponential backoff enabled, actual delays will be: 1s, 2s, 4s, etc.
EXPO_PUBLIC_API_RETRY_DELAY=1000

# Production Configuration Examples:
# For production deployment, you might want to:
# - Increase timeout for slower networks: EXPO_PUBLIC_API_TIMEOUT=60000
# - Reduce retries for faster failure detection: EXPO_PUBLIC_API_RETRY_ATTEMPTS=2
# - Use a load balancer URL: EXPO_PUBLIC_API_BASE_URL=https://api.yourcompany.com

# Development vs Production URLs:
# Development: http://127.0.0.1:11434 or http://localhost:11434
# Staging: https://staging-api.yourcompany.com
# Production: https://api.yourcompany.com

# Security Note:
# In React Native/Expo, environment variables prefixed with EXPO_PUBLIC_ 
# are bundled into the app and visible to users. For sensitive data like 
# API keys, use secure storage or server-side proxy instead.

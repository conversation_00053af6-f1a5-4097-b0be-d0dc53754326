import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Dimensions,
  TouchableOpacity 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ChartBar as BarChart3, TrendingUp, Clock, FileText, MessageCircle, Users, Target, Award, Calendar, Activity } from 'lucide-react-native';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

const { width } = Dimensions.get('window');

const metrics = [
  {
    title: 'Documents Analyzed',
    value: '24',
    change: '+12%',
    trend: 'up',
    icon: FileText,
    color: '#6366F1'
  },
  {
    title: 'Questions Asked',
    value: '156',
    change: '+8%',
    trend: 'up',
    icon: MessageCircle,
    color: '#10B981'
  },
  {
    title: 'Avg Response Time',
    value: '1.2s',
    change: '-15%',
    trend: 'down',
    icon: Clock,
    color: '#F59E0B'
  },
  {
    title: 'Accuracy Score',
    value: '94%',
    change: '+2%',
    trend: 'up',
    icon: Target,
    color: '#EF4444'
  }
];

const recentActivity = [
  {
    type: 'document',
    title: 'Employee Handbook uploaded',
    time: '2 hours ago',
    icon: FileText
  },
  {
    type: 'chat',
    title: '12 questions answered',
    time: '4 hours ago',
    icon: MessageCircle
  },
  {
    type: 'analysis',
    title: 'Policy analysis completed',
    time: '1 day ago',
    icon: BarChart3
  },
  {
    type: 'achievement',
    title: 'Reached 100 interactions',
    time: '2 days ago',
    icon: Award
  }
];

const weeklyData = [
  { day: 'Mon', value: 12 },
  { day: 'Tue', value: 19 },
  { day: 'Wed', value: 8 },
  { day: 'Thu', value: 15 },
  { day: 'Fri', value: 22 },
  { day: 'Sat', value: 6 },
  { day: 'Sun', value: 4 }
];

export default function AnalyticsScreen() {
  const maxValue = Math.max(...weeklyData.map(d => d.value));

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.delay(200)} style={styles.header}>
          <Text style={styles.title}>Analytics</Text>
          <Text style={styles.subtitle}>
            Track your HR assistant usage and performance
          </Text>
        </Animated.View>

        {/* Metrics Grid */}
        <Animated.View entering={FadeInUp.delay(400)} style={styles.metricsGrid}>
          {metrics.map((metric, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(600 + index * 100)}
              style={styles.metricCard}
            >
              <LinearGradient
                colors={[`${metric.color}15`, `${metric.color}05`]}
                style={styles.metricGradient}
              >
                <View style={styles.metricHeader}>
                  <View style={[styles.metricIcon, { backgroundColor: `${metric.color}20` }]}>
                    <metric.icon size={20} color={metric.color} strokeWidth={2.5} />
                  </View>
                  <View style={[
                    styles.changeIndicator,
                    { backgroundColor: metric.trend === 'up' ? '#10B98120' : '#EF444420' }
                  ]}>
                    <TrendingUp 
                      size={12} 
                      color={metric.trend === 'up' ? '#10B981' : '#EF4444'} 
                      strokeWidth={2.5}
                      style={{ 
                        transform: [{ rotate: metric.trend === 'up' ? '0deg' : '180deg' }] 
                      }}
                    />
                    <Text style={[
                      styles.changeText,
                      { color: metric.trend === 'up' ? '#10B981' : '#EF4444' }
                    ]}>
                      {metric.change}
                    </Text>
                  </View>
                </View>
                <Text style={styles.metricValue}>{metric.value}</Text>
                <Text style={styles.metricTitle}>{metric.title}</Text>
              </LinearGradient>
            </Animated.View>
          ))}
        </Animated.View>

        {/* Weekly Chart */}
        <Animated.View entering={FadeInUp.delay(1000)} style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Weekly Activity</Text>
          <View style={styles.chartContainer}>
            <LinearGradient
              colors={['#FFFFFF', '#F8FAFC']}
              style={styles.chartGradient}
            >
              <View style={styles.chart}>
                {weeklyData.map((item, index) => (
                  <View key={index} style={styles.chartBar}>
                    <View style={styles.barContainer}>
                      <LinearGradient
                        colors={['#6366F1', '#8B5CF6']}
                        style={[
                          styles.bar,
                          { height: (item.value / maxValue) * 80 }
                        ]}
                      />
                    </View>
                    <Text style={styles.barLabel}>{item.day}</Text>
                    <Text style={styles.barValue}>{item.value}</Text>
                  </View>
                ))}
              </View>
            </LinearGradient>
          </View>
        </Animated.View>

        {/* Recent Activity */}
        <Animated.View entering={FadeInUp.delay(1200)} style={styles.activitySection}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          {recentActivity.map((activity, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(1400 + index * 100)}
              style={styles.activityItem}
            >
              <View style={styles.activityIcon}>
                <activity.icon size={18} color="#6366F1" strokeWidth={2.5} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>{activity.title}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </Animated.View>
          ))}
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(1600)} style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionButton} activeOpacity={0.8}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6']}
                style={styles.actionGradient}
              >
                <Calendar size={20} color="#FFFFFF" strokeWidth={2.5} />
                <Text style={styles.actionText}>Export Report</Text>
              </LinearGradient>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} activeOpacity={0.8}>
              <LinearGradient
                colors={['#10B981', '#059669']}
                style={styles.actionGradient}
              >
                <Activity size={20} color="#FFFFFF" strokeWidth={2.5} />
                <Text style={styles.actionText}>View Insights</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    marginBottom: 32,
    gap: 12,
  },
  metricCard: {
    width: (width - 60) / 2,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  metricGradient: {
    padding: 16,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  changeText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#64748B',
  },
  chartSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    marginBottom: 16,
  },
  chartContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  chartGradient: {
    padding: 20,
  },
  chart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  barContainer: {
    height: 80,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 20,
    borderRadius: 10,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#64748B',
    marginBottom: 2,
  },
  barValue: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#6366F1',
  },
  activitySection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  activityIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1E293B',
    marginBottom: 2,
  },
  activityTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
  },
  actionsSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});
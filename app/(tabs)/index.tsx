import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Dimensions,
  Platform,
  Image
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as DocumentPicker from 'expo-document-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { Upload, FileText, ArrowRight, Sparkles, Shield, Zap, CircleCheck as CheckCircle, Clock, Users } from 'lucide-react-native';
import Animated, { 
  FadeInDown, 
  FadeInUp, 
  useSharedValue, 
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  Easing
} from 'react-native-reanimated';
import { DocumentUploadCard } from '@/components/DocumentUploadCard';
import { useAppStore } from '@/store/useAppStore';

const { width } = Dimensions.get('window');

const features = [
  {
    icon: Sparkles,
    title: 'AI-Powered Analysis',
    description: 'Advanced natural language processing for document insights',
    color: '#8B5CF6'
  },
  {
    icon: Shield,
    title: 'Secure & Private',
    description: 'Enterprise-grade security for your sensitive HR documents',
    color: '#10B981'
  },
  {
    icon: Zap,
    title: 'Instant Responses',
    description: 'Get immediate answers to your HR policy questions',
    color: '#F59E0B'
  }
];

const stats = [
  { label: 'Documents Processed', value: '10K+', icon: FileText },
  { label: 'Average Response Time', value: '<2s', icon: Clock },
  { label: 'Active Users', value: '500+', icon: Users }
];

export default function UploadScreen() {
  const [isUploading, setIsUploading] = useState(false);
  const { selectedDocument, setSelectedDocument, recentDocuments } = useAppStore();
  const pulseValue = useSharedValue(1);
  const insets = useSafeAreaInsets();

  React.useEffect(() => {
    pulseValue.value = withRepeat(
      withTiming(1.04, { duration: 1200, easing: Easing.inOut(Easing.ease) }),
      -1,
      true
    );
  }, []);

  const animatedPulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseValue.value }],
  }));

  const handleDocumentPick = async () => {
    try {
      setIsUploading(true);
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets.length > 0) {
        const document = result.assets[0];
        setSelectedDocument({
          name: document.name,
          size: document.size,
          uri: document.uri,
          mimeType: document.mimeType,
        });
      }
    } catch (error) {
      console.error('Document selection error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleStartChat = () => {
    if (selectedDocument) {
      router.push('/(tabs)/chat');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: Math.max(120, insets.bottom + 20) }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Section */}
        <Animated.View entering={FadeInUp.delay(200)} style={styles.heroSection}>
          <LinearGradient
            colors={['#6366F1', '#8B5CF6', '#EC4899']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.logoGradient}
          >
            <FileText size={32} color="#FFFFFF" strokeWidth={2.5} />
          </LinearGradient>
          
          <Text style={styles.title}>HR Assistant</Text>
          <Text style={styles.subtitle}>
            Transform your HR documents into intelligent conversations. 
            Upload, analyze, and get instant insights powered by AI.
          </Text>

          {/* Hero Image */}
          <View style={styles.heroImageContainer}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800' }}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(99, 102, 241, 0.1)']}
              style={styles.heroImageOverlay}
            />
          </View>
        </Animated.View>

        {/* Stats Section */}
        <Animated.View entering={FadeInUp.delay(400)} style={styles.statsSection}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <stat.icon size={20} color="#6366F1" strokeWidth={2.5} />
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </Animated.View>

        {/* Upload Section */}
        <Animated.View entering={FadeInDown.delay(600)} style={styles.uploadSection}>
          <Text style={styles.sectionTitle}>Upload Document</Text>
          
          {selectedDocument ? (
            <DocumentUploadCard
              document={selectedDocument}
              onRemove={() => setSelectedDocument(null)}
              onReplace={handleDocumentPick}
            />
          ) : (
            <Animated.View style={animatedPulseStyle}>
              <TouchableOpacity
                style={styles.uploadButton}
                onPress={handleDocumentPick}
                disabled={isUploading}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={isUploading ? ['#F3F4F6', '#F3F4F6'] : ['#6366F1', '#8B5CF6']}
                  style={styles.uploadGradient}
                >
                  <Upload 
                    size={28} 
                    color={isUploading ? '#9CA3AF' : '#FFFFFF'} 
                    strokeWidth={2.5} 
                  />
                  <Text style={[styles.uploadText, isUploading && styles.uploadTextDisabled]}>
                    {isUploading ? 'Selecting Document...' : 'Select PDF Document'}
                  </Text>
                  <Text style={styles.uploadHint}>
                    Drag & drop or tap to browse your files
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          )}
        </Animated.View>

        {/* Features Section */}
        <Animated.View entering={FadeInUp.delay(800)} style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Why Choose Our AI Assistant?</Text>
          
          {features.map((feature, index) => (
            <Animated.View 
              key={index}
              entering={FadeInDown.delay(1000 + index * 200)}
              style={styles.featureCard}
            >
              <View style={[styles.featureIcon, { backgroundColor: `${feature.color}15` }]}>
                <feature.icon size={24} color={feature.color} strokeWidth={2.5} />
              </View>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            </Animated.View>
          ))}
        </Animated.View>

        {/* Recent Documents */}
        {recentDocuments.length > 0 && (
          <Animated.View entering={FadeInUp.delay(1400)} style={styles.recentSection}>
            <Text style={styles.sectionTitle}>Recent Documents</Text>
            {recentDocuments.slice(0, 3).map((doc, index) => (
              <TouchableOpacity
                key={index}
                style={styles.recentItem}
                onPress={() => setSelectedDocument(doc)}
              >
                <FileText size={20} color="#6366F1" strokeWidth={2.5} />
                <Text style={styles.recentName} numberOfLines={1}>{doc.name}</Text>
                <CheckCircle size={16} color="#10B981" strokeWidth={2.5} />
              </TouchableOpacity>
            ))}
          </Animated.View>
        )}

        {/* Action Button */}
        <Animated.View entering={FadeInUp.delay(1600)} style={styles.actionSection}>
          <TouchableOpacity
            style={[
              styles.startChatButton,
              !selectedDocument && styles.startChatButtonDisabled,
            ]}
            onPress={handleStartChat}
            disabled={!selectedDocument}
            activeOpacity={0.9}
          >
            <LinearGradient
              colors={selectedDocument ? ['#6366F1', '#8B5CF6'] : ['#F3F4F6', '#F3F4F6']}
              style={styles.buttonGradient}
            >
              <Text style={[
                styles.startChatText,
                !selectedDocument && styles.startChatTextDisabled,
              ]}>
                Start AI Conversation
              </Text>
              <ArrowRight 
                size={20} 
                color={selectedDocument ? '#FFFFFF' : '#9CA3AF'} 
                strokeWidth={2.5} 
              />
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  heroSection: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  logoGradient: {
    width: 72,
    height: 72,
    borderRadius: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  heroImageContainer: {
    width: width - 48,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  statsSection: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#64748B',
    textAlign: 'center',
  },
  uploadSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    marginBottom: 16,
  },
  uploadButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  uploadGradient: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginTop: 12,
    marginBottom: 4,
  },
  uploadTextDisabled: {
    color: '#9CA3AF',
  },
  uploadHint: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  featuresSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  featureCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
    justifyContent: 'center',
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
    lineHeight: 20,
  },
  recentSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  recentName: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1E293B',
    marginLeft: 12,
    marginRight: 12,
  },
  actionSection: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  startChatButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 4,
  },
  startChatButtonDisabled: {
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
  },
  startChatText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginRight: 8,
  },
  startChatTextDisabled: {
    color: '#9CA3AF',
  },
});
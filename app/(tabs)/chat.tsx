import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Send, FileText, ArrowLeft, Sparkles, Copy, ThumbsUp, ThumbsDown, MoveHorizontal as MoreHorizontal } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { 
  FadeInDown, 
  FadeInUp, 
  SlideInRight,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence
} from 'react-native-reanimated';
import { ChatMessage } from '@/components/ChatMessage';
import { LoadingIndicator } from '@/components/LoadingIndicator';
import { useAppStore } from '@/store/useAppStore';
import { sendMessage } from '@/services/api';
import { Message } from '@/types';

const { width } = Dimensions.get('window');

const suggestedQuestions = [
  "What are the key policies in this document?",
  "Summarize the main points for me",
  "What are the employee benefits mentioned?",
  "Are there any compliance requirements?",
];

export default function ChatScreen() {
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const { selectedDocument, messages, addMessage } = useAppStore();
  const insets = useSafeAreaInsets();
  
  const sparkleValue = useSharedValue(0);

  useEffect(() => {
    if (!selectedDocument) {
      router.replace('/(tabs)');
    }
  }, [selectedDocument]);

  useEffect(() => {
    scrollToBottom();
    if (messages.length > 0) {
      setShowSuggestions(false);
    }
  }, [messages]);

  useEffect(() => {
    sparkleValue.value = withRepeat(
      withSequence(
        withSpring(1, { duration: 1500 }),
        withSpring(0, { duration: 1500 })
      ),
      -1,
      true
    );
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (event) => {
        setIsKeyboardVisible(true);
        setKeyboardHeight(event.endCoordinates.height);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const animatedSparkleStyle = useAnimatedStyle(() => ({
    opacity: sparkleValue.value,
    transform: [{ rotate: `${sparkleValue.value * 360}deg` }],
  }));

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleSendMessage = async (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText || !selectedDocument || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      isUser: true,
      timestamp: new Date(),
    };

    addMessage(userMessage);
    setInputText('');
    setIsLoading(true);
    setShowSuggestions(false);

    try {
      const response = await sendMessage(messageText, selectedDocument.uri);
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        isUser: false,
        timestamp: new Date(),
      };

      addMessage(aiMessage);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: 'I apologize, but I encountered an error while processing your request. Please check your connection and try again.',
        isUser: false,
        timestamp: new Date(),
        isError: true,
      };
      addMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionPress = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleBackToUpload = () => {
    router.push('/(tabs)');
  };

  if (!selectedDocument) {
    return null;
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <Animated.View entering={FadeInDown.delay(200)} style={styles.header}>
        <LinearGradient
          colors={['#6366F1', '#8B5CF6']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.headerGradient}
        >
          <TouchableOpacity onPress={handleBackToUpload} style={styles.backButton}>
            <ArrowLeft size={20} color="#FFFFFF" strokeWidth={2.5} />
          </TouchableOpacity>
          
          <View style={styles.documentInfo}>
            <View style={styles.documentIcon}>
              <FileText size={16} color="#6366F1" strokeWidth={2.5} />
            </View>
            <View style={styles.documentDetails}>
              <Text style={styles.documentName} numberOfLines={1}>
                {selectedDocument.name}
              </Text>
              <Text style={styles.documentStatus}>Ready for analysis</Text>
            </View>
          </View>

          <Animated.View style={animatedSparkleStyle}>
            <Sparkles size={20} color="#FFFFFF" strokeWidth={2.5} />
          </Animated.View>
        </LinearGradient>
      </Animated.View>

      {/* Messages Container */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        contentContainerStyle={[
          styles.messagesContent,
          { paddingBottom: isKeyboardVisible ? keyboardHeight + 100 : 100 }
        ]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {messages.length === 0 && showSuggestions && (
          <Animated.View entering={FadeInUp.delay(400)} style={styles.welcomeContainer}>
            <LinearGradient
              colors={['#EBF4FF', '#F0F9FF']}
              style={styles.welcomeGradient}
            >
              <View style={styles.welcomeIcon}>
                <Sparkles size={24} color="#6366F1" strokeWidth={2.5} />
              </View>
              <Text style={styles.welcomeTitle}>
                Hello! I'm your AI HR Assistant
              </Text>
              <Text style={styles.welcomeText}>
                I've analyzed your document and I'm ready to help. Ask me anything about your HR policies, procedures, or any specific information you need.
              </Text>
            </LinearGradient>
          </Animated.View>
        )}

        {/* Suggested Questions */}
        {showSuggestions && messages.length === 0 && (
          <Animated.View entering={FadeInUp.delay(600)} style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsTitle}>Try asking:</Text>
            {suggestedQuestions.map((question, index) => (
              <Animated.View
                key={index}
                entering={SlideInRight.delay(800 + index * 100)}
              >
                <TouchableOpacity
                  style={styles.suggestionButton}
                  onPress={() => handleSuggestionPress(question)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.suggestionText}>{question}</Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </Animated.View>
        )}
        
        {messages.map((message, index) => (
          <Animated.View
            key={message.id}
            entering={FadeInUp.delay(index * 100)}
          >
            <ChatMessage message={message} />
          </Animated.View>
        ))}
        
        {isLoading && (
          <Animated.View entering={FadeInUp}>
            <LoadingIndicator />
          </Animated.View>
        )}
      </ScrollView>

      {/* Input Container */}
      <Animated.View 
        entering={FadeInUp.delay(1000)} 
        style={[
          styles.inputContainer,
          {
            bottom: isKeyboardVisible ? keyboardHeight : insets.bottom,
          }
        ]}
      >
        {Platform.OS === 'ios' ? (
          <BlurView intensity={95} style={styles.inputBlur}>
            <View style={styles.inputContent}>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.textInput}
                  value={inputText}
                  onChangeText={setInputText}
                  placeholder="Ask about your HR document..."
                  placeholderTextColor="#94A3B8"
                  multiline
                  maxLength={500}
                  editable={!isLoading}
                  onFocus={() => setIsKeyboardVisible(true)}
                  onBlur={() => setIsKeyboardVisible(false)}
                />
                <TouchableOpacity
                  style={[
                    styles.sendButton,
                    (!inputText.trim() || isLoading) && styles.sendButtonDisabled,
                  ]}
                  onPress={() => handleSendMessage()}
                  disabled={!inputText.trim() || isLoading}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={(!inputText.trim() || isLoading) ? ['#E2E8F0', '#E2E8F0'] : ['#6366F1', '#8B5CF6']}
                    style={styles.sendButtonGradient}
                  >
                    <Send 
                      size={18} 
                      color={(!inputText.trim() || isLoading) ? '#94A3B8' : '#FFFFFF'} 
                      strokeWidth={2.5} 
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </BlurView>
        ) : (
          <View style={styles.inputContent}>
            <View style={styles.inputWrapper}>
              <TextInput
                style={styles.textInput}
                value={inputText}
                onChangeText={setInputText}
                placeholder="Ask about your HR document..."
                placeholderTextColor="#94A3B8"
                multiline
                maxLength={500}
                editable={!isLoading}
                onFocus={() => setIsKeyboardVisible(true)}
                onBlur={() => setIsKeyboardVisible(false)}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  (!inputText.trim() || isLoading) && styles.sendButtonDisabled,
                ]}
                onPress={() => handleSendMessage()}
                disabled={!inputText.trim() || isLoading}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={(!inputText.trim() || isLoading) ? ['#E2E8F0', '#E2E8F0'] : ['#6366F1', '#8B5CF6']}
                  style={styles.sendButtonGradient}
                >
                  <Send 
                    size={18} 
                    color={(!inputText.trim() || isLoading) ? '#94A3B8' : '#FFFFFF'} 
                    strokeWidth={2.5} 
                  />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFF',
  },
  header: {
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  documentIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentDetails: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  documentStatus: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 20,
    paddingBottom: 8,
  },
  welcomeContainer: {
    marginBottom: 24,
  },
  welcomeGradient: {
    borderRadius: 16,
    padding: 20,
  },
  welcomeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    alignSelf: 'center',
  },
  welcomeTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 20,
  },
  suggestionsContainer: {
    marginBottom: 24,
  },
  suggestionsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#64748B',
    marginBottom: 12,
  },
  suggestionButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  suggestionText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#475569',
  },
  inputContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: Platform.OS === 'ios' ? 'transparent' : '#FFFFFF',
    borderTopWidth: Platform.OS === 'ios' ? 0 : 1,
    borderTopColor: '#E2E8F0',
    paddingBottom: 20,
  },
  inputBlur: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  inputContent: {
    padding: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingHorizontal: 20,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 1,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1E293B',
    maxHeight: 100,
    paddingVertical: 8,
    paddingRight: 12,
  },
  sendButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendButtonGradient: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Switch,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Settings as SettingsIcon, User, Bell, Shield, Palette, CircleHelp as HelpCircle, Info, LogOut, ChevronRight, Moon, Globe, Download, Trash2 } from 'lucide-react-native';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: any;
  type: 'navigation' | 'toggle' | 'action';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  color?: string;
}

export default function SettingsScreen() {
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [autoSave, setAutoSave] = useState(true);

  const settingSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'Profile Settings',
          subtitle: 'Manage your account information',
          icon: User,
          type: 'navigation',
          onPress: () => Alert.alert('Profile', 'Profile settings coming soon!')
        },
        {
          id: 'notifications',
          title: 'Notifications',
          subtitle: 'Push notifications and alerts',
          icon: Bell,
          type: 'toggle',
          value: notifications,
          onToggle: setNotifications
        }
      ]
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'darkMode',
          title: 'Dark Mode',
          subtitle: 'Switch to dark theme',
          icon: Moon,
          type: 'toggle',
          value: darkMode,
          onToggle: setDarkMode
        },
        {
          id: 'language',
          title: 'Language',
          subtitle: 'English (US)',
          icon: Globe,
          type: 'navigation',
          onPress: () => Alert.alert('Language', 'Language settings coming soon!')
        },
        {
          id: 'autoSave',
          title: 'Auto-save Conversations',
          subtitle: 'Automatically save chat history',
          icon: Download,
          type: 'toggle',
          value: autoSave,
          onToggle: setAutoSave
        }
      ]
    },
    {
      title: 'Security & Privacy',
      items: [
        {
          id: 'privacy',
          title: 'Privacy Settings',
          subtitle: 'Data usage and privacy controls',
          icon: Shield,
          type: 'navigation',
          onPress: () => Alert.alert('Privacy', 'Privacy settings coming soon!')
        },
        {
          id: 'clearData',
          title: 'Clear Chat History',
          subtitle: 'Delete all conversation data',
          icon: Trash2,
          type: 'action',
          color: '#EF4444',
          onPress: () => Alert.alert(
            'Clear Data',
            'Are you sure you want to delete all chat history? This action cannot be undone.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Delete', style: 'destructive', onPress: () => {} }
            ]
          )
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          icon: HelpCircle,
          type: 'navigation',
          onPress: () => Alert.alert('Help', 'Help center coming soon!')
        },
        {
          id: 'about',
          title: 'About',
          subtitle: 'App version and information',
          icon: Info,
          type: 'navigation',
          onPress: () => Alert.alert('About', 'HR Assistant v1.0.0\nBuilt with ❤️ for better HR management')
        }
      ]
    }
  ];

  const renderSettingItem = (item: SettingItem, index: number) => {
    return (
      <Animated.View
        key={item.id}
        entering={FadeInUp.delay(600 + index * 100)}
        style={styles.settingItem}
      >
        <TouchableOpacity
          style={styles.settingButton}
          onPress={item.onPress}
          activeOpacity={0.7}
          disabled={item.type === 'toggle'}
        >
          <View style={styles.settingLeft}>
            <View style={[
              styles.settingIcon,
              { backgroundColor: item.color ? `${item.color}15` : '#EBF4FF' }
            ]}>
              <item.icon 
                size={20} 
                color={item.color || '#6366F1'} 
                strokeWidth={2.5} 
              />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>{item.title}</Text>
              {item.subtitle && (
                <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
              )}
            </View>
          </View>
          
          <View style={styles.settingRight}>
            {item.type === 'toggle' && (
              <Switch
                value={item.value}
                onValueChange={item.onToggle}
                trackColor={{ false: '#E2E8F0', true: '#6366F1' }}
                thumbColor={item.value ? '#FFFFFF' : '#FFFFFF'}
              />
            )}
            {item.type === 'navigation' && (
              <ChevronRight size={20} color="#94A3B8" strokeWidth={2.5} />
            )}
            {item.type === 'action' && (
              <ChevronRight size={20} color={item.color || '#94A3B8'} strokeWidth={2.5} />
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.delay(200)} style={styles.header}>
          <LinearGradient
            colors={['#6366F1', '#8B5CF6']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerGradient}
          >
            <SettingsIcon size={24} color="#FFFFFF" strokeWidth={2.5} />
          </LinearGradient>
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>
            Customize your HR assistant experience
          </Text>
        </Animated.View>

        {/* Settings Sections */}
        {settingSections.map((section, sectionIndex) => (
          <Animated.View
            key={section.title}
            entering={FadeInUp.delay(400 + sectionIndex * 200)}
            style={styles.section}
          >
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => 
                renderSettingItem(item, sectionIndex * 10 + itemIndex)
              )}
            </View>
          </Animated.View>
        ))}

        {/* Sign Out Button */}
        <Animated.View entering={FadeInUp.delay(1200)} style={styles.signOutSection}>
          <TouchableOpacity
            style={styles.signOutButton}
            onPress={() => Alert.alert(
              'Sign Out',
              'Are you sure you want to sign out?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Sign Out', style: 'destructive', onPress: () => {} }
              ]
            )}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#EF4444', '#DC2626']}
              style={styles.signOutGradient}
            >
              <LogOut size={20} color="#FFFFFF" strokeWidth={2.5} />
              <Text style={styles.signOutText}>Sign Out</Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* App Version */}
        <Animated.View entering={FadeInUp.delay(1400)} style={styles.versionSection}>
          <Text style={styles.versionText}>HR Assistant v1.0.0</Text>
          <Text style={styles.versionSubtext}>Made with ❤️ for better HR management</Text>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  headerGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#1E293B',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1E293B',
    marginBottom: 12,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1E293B',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#64748B',
  },
  settingRight: {
    marginLeft: 12,
  },
  signOutSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  signOutButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#EF4444',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  signOutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  signOutText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  versionSection: {
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  versionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#94A3B8',
    marginBottom: 4,
  },
  versionSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#CBD5E1',
  },
});
import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import { Upload, MessageCircle, Settings, ChartBar as BarChart3 } from 'lucide-react-native';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { usePathname } from 'expo-router';

export default function TabLayout() {
  const insets = useSafeAreaInsets();
  const pathname = usePathname();
  const isChatScreen = pathname.includes('/chat');

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#6366F1',
        tabBarInactiveTintColor: '#64748B',
        tabBarStyle: {
          backgroundColor: Platform.OS === 'ios' ? 'transparent' : '#FFFFFF',
          borderTopWidth: 0,
          elevation: 0,
          shadowOpacity: 0,
          paddingTop: 8,
          paddingBottom: Platform.OS === 'ios' ? Math.max(32, insets.bottom) : Math.max(28, insets.bottom),
          height: Platform.OS === 'ios' ? 88 + insets.bottom : 72 + insets.bottom,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          display: isChatScreen ? 'none' : 'flex',
        },
        tabBarBackground: Platform.OS === 'ios' ? () => (
          <BlurView
            intensity={95}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
            }}
          />
        ) : undefined,
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Inter-Medium',
          marginTop: 4,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Upload',
          tabBarIcon: ({ size, color }) => (
            <Upload size={size} color={color} strokeWidth={2.5} />
          ),
        }}
      />
      <Tabs.Screen
        name="chat"
        options={{
          title: 'Chat',
          tabBarIcon: ({ size, color }) => (
            <MessageCircle size={size} color={color} strokeWidth={2.5} />
          ),
          tabBarStyle: { display: 'none' },
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          title: 'Analytics',
          tabBarIcon: ({ size, color }) => (
            <BarChart3 size={size} color={color} strokeWidth={2.5} />
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ size, color }) => (
            <Settings size={size} color={color} strokeWidth={2.5} />
          ),
        }}
      />
    </Tabs>
  );
}
/**
 * Test suite for the production-ready Axios API service
 * Run with: npm test or yarn test
 */

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import ApiService, { apiService, ApiServiceError, GenerateRequest, GenerateResponse } from '../api';

// Mock axios for testing
const mockAxios = new MockAdapter(axios);

describe('ApiService', () => {
  let testApiService: ApiService;

  beforeEach(() => {
    // Create a fresh instance for each test
    testApiService = new ApiService();
    mockAxios.reset();
  });

  afterEach(() => {
    mockAxios.reset();
  });

  describe('Configuration', () => {
    it('should initialize with default configuration', () => {
      const config = testApiService.getConfig();
      
      expect(config.baseURL).toBe('http://127.0.0.1:11434');
      expect(config.timeout).toBe(30000);
      expect(config.retryConfig.attempts).toBe(3);
      expect(config.retryConfig.delay).toBe(1000);
    });

    it('should allow updating retry configuration', () => {
      testApiService.updateRetryConfig({
        attempts: 5,
        delay: 2000,
      });

      const config = testApiService.getConfig();
      expect(config.retryConfig.attempts).toBe(5);
      expect(config.retryConfig.delay).toBe(2000);
    });
  });

  describe('Generate API', () => {
    const mockRequest: GenerateRequest = {
      model: 'deepseek-r1:14b',
      prompt: 'Why is the sky blue?',
      stream: false,
    };

    const mockResponse: GenerateResponse = {
      model: 'deepseek-r1:14b',
      created_at: '2024-01-01T00:00:00Z',
      response: 'The sky appears blue due to Rayleigh scattering...',
      done: true,
      total_duration: 1000000,
      eval_count: 50,
    };

    it('should successfully generate a response', async () => {
      mockAxios.onPost('/api/generate').reply(200, mockResponse);

      const result = await testApiService.generate(mockRequest);

      expect(result).toEqual(mockResponse);
      expect(mockAxios.history.post).toHaveLength(1);
      expect(JSON.parse(mockAxios.history.post[0].data)).toEqual(mockRequest);
    });

    it('should handle sendPrompt convenience method', async () => {
      mockAxios.onPost('/api/generate').reply(200, mockResponse);

      const result = await testApiService.sendPrompt('deepseek-r1:14b', 'Why is the sky blue?');

      expect(result).toBe(mockResponse.response);
    });

    it('should handle server errors properly', async () => {
      const errorResponse = { error: 'Model not found' };
      mockAxios.onPost('/api/generate').reply(404, errorResponse);

      await expect(testApiService.generate(mockRequest)).rejects.toThrow(ApiServiceError);
      
      try {
        await testApiService.generate(mockRequest);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiServiceError);
        expect((error as ApiServiceError).status).toBe(404);
        expect((error as ApiServiceError).code).toBe('HTTP_ERROR');
        expect((error as ApiServiceError).message).toBe('Model not found');
      }
    });

    it('should handle network errors', async () => {
      mockAxios.onPost('/api/generate').networkError();

      await expect(testApiService.generate(mockRequest)).rejects.toThrow(ApiServiceError);
      
      try {
        await testApiService.generate(mockRequest);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiServiceError);
        expect((error as ApiServiceError).code).toBe('NETWORK_ERROR');
      }
    });

    it('should handle timeout errors', async () => {
      mockAxios.onPost('/api/generate').timeout();

      await expect(testApiService.generate(mockRequest)).rejects.toThrow(ApiServiceError);
    });
  });

  describe('Health Check', () => {
    it('should return true when API is healthy', async () => {
      mockAxios.onGet('/api/version').reply(200, { version: '1.0.0' });

      const isHealthy = await testApiService.healthCheck();

      expect(isHealthy).toBe(true);
    });

    it('should return false when API is unhealthy', async () => {
      mockAxios.onGet('/api/version').reply(500);

      const isHealthy = await testApiService.healthCheck();

      expect(isHealthy).toBe(false);
    });

    it('should return false on network error', async () => {
      mockAxios.onGet('/api/version').networkError();

      const isHealthy = await testApiService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });

  describe('Retry Logic', () => {
    it('should retry on server errors', async () => {
      const mockRequest: GenerateRequest = {
        model: 'test-model',
        prompt: 'test prompt',
      };

      // First two calls fail, third succeeds
      mockAxios
        .onPost('/api/generate')
        .replyOnce(500, { error: 'Server error' })
        .onPost('/api/generate')
        .replyOnce(500, { error: 'Server error' })
        .onPost('/api/generate')
        .replyOnce(200, { response: 'Success', done: true });

      const result = await testApiService.generate(mockRequest);

      expect(result.response).toBe('Success');
      expect(mockAxios.history.post).toHaveLength(3);
    });

    it('should not retry on client errors (4xx)', async () => {
      const mockRequest: GenerateRequest = {
        model: 'test-model',
        prompt: 'test prompt',
      };

      mockAxios.onPost('/api/generate').reply(400, { error: 'Bad request' });

      await expect(testApiService.generate(mockRequest)).rejects.toThrow(ApiServiceError);
      
      // Should only make one request (no retries)
      expect(mockAxios.history.post).toHaveLength(1);
    });

    it('should retry on 429 (rate limit) errors', async () => {
      const mockRequest: GenerateRequest = {
        model: 'test-model',
        prompt: 'test prompt',
      };

      // First call rate limited, second succeeds
      mockAxios
        .onPost('/api/generate')
        .replyOnce(429, { error: 'Rate limited' })
        .onPost('/api/generate')
        .replyOnce(200, { response: 'Success', done: true });

      const result = await testApiService.generate(mockRequest);

      expect(result.response).toBe('Success');
      expect(mockAxios.history.post).toHaveLength(2);
    });
  });

  describe('Custom Requests', () => {
    it('should handle custom requests', async () => {
      const mockData = { name: 'test-model', size: 1000 };
      mockAxios.onPost('/api/show').reply(200, mockData);

      const result = await testApiService.customRequest({
        method: 'POST',
        url: '/api/show',
        data: { name: 'test-model' },
      });

      expect(result).toEqual(mockData);
    });
  });

  describe('Singleton Instance', () => {
    it('should provide a singleton instance', () => {
      expect(apiService).toBeInstanceOf(ApiService);
      
      // Should be the same instance
      const config1 = apiService.getConfig();
      apiService.updateRetryConfig({ attempts: 10 });
      const config2 = apiService.getConfig();
      
      expect(config2.retryConfig.attempts).toBe(10);
    });
  });
});

// Integration tests (these would run against a real API in a test environment)
describe('Integration Tests', () => {
  // Skip these tests by default since they require a running Ollama instance
  describe.skip('Real API Tests', () => {
    it('should connect to real Ollama API', async () => {
      const realApiService = new ApiService();
      
      const isHealthy = await realApiService.healthCheck();
      
      if (isHealthy) {
        const response = await realApiService.sendPrompt(
          'deepseek-r1:14b',
          'Say "Hello, World!" and nothing else.'
        );
        
        expect(response).toContain('Hello');
      } else {
        console.warn('Skipping real API test - Ollama not available');
      }
    });
  });
});

// Performance tests
describe('Performance Tests', () => {
  it('should handle concurrent requests', async () => {
    const mockResponse = {
      model: 'test-model',
      response: 'Test response',
      done: true,
    };

    // Mock multiple successful responses
    for (let i = 0; i < 10; i++) {
      mockAxios.onPost('/api/generate').replyOnce(200, mockResponse);
    }

    const promises = Array.from({ length: 10 }, (_, i) =>
      testApiService.sendPrompt('test-model', `Prompt ${i}`)
    );

    const results = await Promise.all(promises);

    expect(results).toHaveLength(10);
    expect(results.every(result => result === 'Test response')).toBe(true);
  });
});

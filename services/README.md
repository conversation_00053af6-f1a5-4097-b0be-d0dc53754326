# Production-Ready Axios API Service

A robust, production-ready Axios configuration for making HTTP requests to Ollama API endpoints with comprehensive error handling, retry logic, and TypeScript support.

## Features

- ✅ **Production-ready configuration** with environment variable support
- ✅ **Comprehensive error handling** with custom error types
- ✅ **Automatic retry logic** with exponential backoff
- ✅ **Request/response interceptors** for logging and authentication
- ✅ **TypeScript support** with full type safety
- ✅ **Health check functionality** for API monitoring
- ✅ **Configurable timeouts** and retry policies
- ✅ **Singleton pattern** for consistent usage across the app

## Quick Start

```typescript
import { apiService } from './services/api';

// Basic usage - equivalent to the curl command
const response = await apiService.sendPrompt(
  'deepseek-r1:14b',
  'Why is the sky blue?'
);
console.log(response);
```

## Configuration

### Environment Variables

Create a `.env` file in your project root:

```env
EXPO_PUBLIC_API_BASE_URL=http://127.0.0.1:11434
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_API_RETRY_ATTEMPTS=3
EXPO_PUBLIC_API_RETRY_DELAY=1000
```

### Default Configuration

- **Base URL**: `http://127.0.0.1:11434`
- **Timeout**: 30 seconds
- **Retry Attempts**: 3
- **Retry Delay**: 1 second (with exponential backoff)

## API Reference

### Basic Methods

#### `sendPrompt(model: string, prompt: string, options?: Partial<GenerateRequest>): Promise<string>`

Send a simple prompt to a model (convenience method).

```typescript
const response = await apiService.sendPrompt(
  'deepseek-r1:14b',
  'Explain quantum computing'
);
```

#### `generate(request: GenerateRequest): Promise<GenerateResponse>`

Full control over the generation request.

```typescript
const response = await apiService.generate({
  model: 'deepseek-r1:14b',
  prompt: 'Why is the sky blue?',
  stream: false,
  options: {
    temperature: 0.7,
    top_p: 0.9,
    num_predict: 500,
  },
  system: 'You are a helpful assistant.',
});
```

#### `healthCheck(): Promise<boolean>`

Check if the API is available.

```typescript
const isHealthy = await apiService.healthCheck();
if (!isHealthy) {
  console.error('API is not available');
}
```

### Advanced Methods

#### `customRequest<T>(config: AxiosRequestConfig): Promise<T>`

Make custom requests with full Axios configuration.

```typescript
const modelInfo = await apiService.customRequest({
  method: 'POST',
  url: '/api/show',
  data: { name: 'deepseek-r1:14b' },
});
```

#### `updateRetryConfig(config: Partial<RetryConfig>): void`

Update retry configuration at runtime.

```typescript
apiService.updateRetryConfig({
  attempts: 5,
  delay: 2000,
  backoff: true,
});
```

## Error Handling

The service uses a custom `ApiServiceError` class for consistent error handling:

```typescript
try {
  const response = await apiService.sendPrompt('model', 'prompt');
} catch (error) {
  if (error instanceof ApiServiceError) {
    console.error('API Error:', {
      message: error.message,
      status: error.status,
      code: error.code,
    });
    
    switch (error.code) {
      case 'HTTP_ERROR':
        // Handle HTTP errors (4xx, 5xx)
        break;
      case 'NETWORK_ERROR':
        // Handle network connectivity issues
        break;
      case 'UNKNOWN_ERROR':
        // Handle unexpected errors
        break;
    }
  }
}
```

## Retry Logic

The service automatically retries failed requests with the following behavior:

- **Retries on**: 5xx server errors, network errors, timeouts, 429 rate limits
- **No retry on**: 4xx client errors (except 429)
- **Backoff strategy**: Exponential backoff (1s, 2s, 4s, 8s...)
- **Configurable**: Attempts, delay, and backoff strategy

## Production Deployment

### Environment-Specific Configuration

```typescript
// Development
EXPO_PUBLIC_API_BASE_URL=http://127.0.0.1:11434

// Staging
EXPO_PUBLIC_API_BASE_URL=https://staging-api.yourcompany.com

// Production
EXPO_PUBLIC_API_BASE_URL=https://api.yourcompany.com
EXPO_PUBLIC_API_TIMEOUT=60000
EXPO_PUBLIC_API_RETRY_ATTEMPTS=2
```

### Security Considerations

- Environment variables prefixed with `EXPO_PUBLIC_` are bundled into the app
- For sensitive data, use secure storage or server-side proxy
- Consider implementing authentication headers in the request interceptor

### Monitoring and Logging

The service includes built-in logging for development and production monitoring:

```typescript
// Request/response logging in development
// Error logging for production monitoring
// Health check for uptime monitoring
```

## Testing

Run the test suite:

```bash
npm test services/__tests__/api.test.ts
```

The test suite includes:
- Unit tests for all methods
- Error handling scenarios
- Retry logic validation
- Performance tests
- Integration tests (optional)

## Examples

See `services/api-examples.ts` for comprehensive usage examples including:

- Basic prompt generation
- Advanced generation with options
- Health checks
- Batch processing
- Error handling patterns
- Production usage patterns

## Migration from Existing Code

If you're migrating from the existing fetch-based implementation:

```typescript
// Old way
const response = await fetch(`${API_BASE_URL}/api/generate`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ model: 'deepseek-r1:14b', prompt: 'Hello' }),
});

// New way
const response = await apiService.sendPrompt('deepseek-r1:14b', 'Hello');
```

## Contributing

When adding new features:

1. Add TypeScript interfaces for new request/response types
2. Include comprehensive error handling
3. Add unit tests
4. Update documentation
5. Consider backward compatibility

## License

This API service is part of your project and follows the same license terms.


import { ChatResponse } from '@/types';
import OpenAI from 'openai'

const openai = new OpenAI({
  baseURL: 'https://815a-223-123-104-34.ngrok-free.app',

  // required but ignored
  apiKey: 'ollama',
})

const API_BASE_URL = 'http://127.0.0.1:11434'; // Adjust this to your Flask backend URL


// export async function sendMessage(question: string, documentUri: string): Promise<ChatResponse> {

//   try {
//     // const response = await fetch(`${API_BASE_URL}/chat`, {
//     //   method: 'POST',
//     //   headers: {
//     //     'Content-Type': 'application/json',
//     //   },
//     //   body: JSON.stringify({
//     //     question,
//     //     pdf_uri: documentUri,
//     //   }),
//     // });
//     // const response = await openai.chat.completions.create({
//     //   model: "deepseek-r1:14b",
//     //   messages: [{ role: 'user', content: 'Say this is a test' }],
//     // })
//     console.log({ response })
//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error('API Error:', error);
//     throw new Error('Failed to communicate with the AI assistant. Please check your connection and try again.');
//   }
// }
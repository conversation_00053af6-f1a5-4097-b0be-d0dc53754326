
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Environment configuration with fallbacks
const API_CONFIG = {
  BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL || 'http://127.0.0.1:11434',
  TIMEOUT: parseInt(process.env.EXPO_PUBLIC_API_TIMEOUT || '30000', 10),
  RETRY_ATTEMPTS: parseInt(process.env.EXPO_PUBLIC_API_RETRY_ATTEMPTS || '3', 10),
  RETRY_DELAY: parseInt(process.env.EXPO_PUBLIC_API_RETRY_DELAY || '1000', 10),
};

// TypeScript interfaces for API requests and responses
export interface GenerateRequest {
  model: string;
  prompt: string;
  stream?: boolean;
  format?: 'json' | string;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    seed?: number;
    num_predict?: number;
    stop?: string[];
  };
  system?: string;
  template?: string;
  context?: number[];
  raw?: boolean;
  keep_alive?: string | number;
}

export interface GenerateResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

// Custom error class for API errors
export class ApiServiceError extends Error {
  public status?: number;
  public code?: string;
  public details?: any;

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message);
    this.name = 'ApiServiceError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

// Retry configuration
interface RetryConfig {
  attempts: number;
  delay: number;
  backoff?: boolean;
}

// Sleep utility for retry delays
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

// Exponential backoff calculation
const calculateBackoffDelay = (attempt: number, baseDelay: number): number =>
  baseDelay * Math.pow(2, attempt - 1);

class ApiService {
  private axiosInstance: AxiosInstance;
  private retryConfig: RetryConfig;

  constructor() {
    this.retryConfig = {
      attempts: API_CONFIG.RETRY_ATTEMPTS,
      delay: API_CONFIG.RETRY_DELAY,
      backoff: true,
    };

    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for logging and authentication
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Log request in development
        if (__DEV__) {
          console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params,
          });
        }

        // Add authentication headers if needed
        // const token = getAuthToken(); // Implement your auth logic
        // if (token) {
        //   config.headers.Authorization = `Bearer ${token}`;
        // }

        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        // Log response in development
        if (__DEV__) {
          console.log(`[API Response] ${response.status} ${response.config.url}`, {
            data: response.data,
          });
        }
        return response;
      },
      (error: AxiosError) => {
        console.error('[API Response Error]', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
        });

        // Transform axios error to our custom error
        const apiError = this.transformError(error);
        return Promise.reject(apiError);
      }
    );
  }

  private transformError(error: AxiosError): ApiServiceError {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      const message = (data as any)?.error ||
                     (data as any)?.message ||
                     error.message ||
                     'An error occurred';

      return new ApiServiceError(message, status, 'HTTP_ERROR', data);
    } else if (error.request) {
      // Request was made but no response received
      return new ApiServiceError(
        'Network error: Unable to reach the server',
        undefined,
        'NETWORK_ERROR',
        error.request
      );
    } else {
      // Something else happened
      return new ApiServiceError(
        error.message || 'An unexpected error occurred',
        undefined,
        'UNKNOWN_ERROR'
      );
    }
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    retryConfig: RetryConfig = this.retryConfig
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= retryConfig.attempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain error types
        if (error instanceof ApiServiceError) {
          // Don't retry on client errors (4xx) except for specific cases
          if (error.status && error.status >= 400 && error.status < 500 && error.status !== 429) {
            throw error;
          }
        }

        // If this was the last attempt, throw the error
        if (attempt === retryConfig.attempts) {
          throw lastError;
        }

        // Calculate delay for next attempt
        const delay = retryConfig.backoff
          ? calculateBackoffDelay(attempt, retryConfig.delay)
          : retryConfig.delay;

        console.warn(`[API Retry] Attempt ${attempt} failed, retrying in ${delay}ms...`, {
          error: lastError.message,
        });

        await sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Generate a response from the Ollama API
   * Equivalent to: curl http://127.0.0.1:11434/api/generate -d '{"model": "deepseek-r1:14b", "prompt": "Why is the sky blue?"}'
   */
  async generate(request: GenerateRequest): Promise<GenerateResponse> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.post<GenerateResponse>('/api/generate', request);
      return response.data;
    });
  }

  /**
   * Send a simple prompt to a model (convenience method)
   */
  async sendPrompt(model: string, prompt: string, options?: Partial<GenerateRequest>): Promise<string> {
    const request: GenerateRequest = {
      model,
      prompt,
      stream: false,
      ...options,
    };

    const response = await this.generate(request);
    return response.response;
  }

  /**
   * Health check for the API
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.axiosInstance.get('/api/version', { timeout: 5000 });
      return true;
    } catch (error) {
      console.warn('[API Health Check] Failed:', error);
      return false;
    }
  }

  /**
   * Get the current configuration
   */
  getConfig() {
    return {
      baseURL: this.axiosInstance.defaults.baseURL,
      timeout: this.axiosInstance.defaults.timeout,
      retryConfig: this.retryConfig,
    };
  }

  /**
   * Update retry configuration
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
  }

  /**
   * Create a custom request with full control
   */
  async customRequest<T = any>(config: AxiosRequestConfig): Promise<T> {
    return this.executeWithRetry(async () => {
      const response = await this.axiosInstance.request<T>(config);
      return response.data;
    });
  }

  /**
   * Send a message to the AI model with comprehensive error handling
   * Suitable for chat functionality in React Native components
   *
   * @param message - The user's message/prompt
   * @param model - The AI model to use (defaults to 'deepseek-r1:14b')
   * @param systemPrompt - Optional system prompt to guide the AI's behavior
   * @param performHealthCheck - Whether to check API health before sending (defaults to true)
   * @returns Promise<string> - The AI's response text
   */
  async sendMessage(
    message: string,
    model: string = 'deepseek-r1:14b',
    systemPrompt?: string,
    performHealthCheck: boolean = true
  ): Promise<string> {
    try {
      // Validate input
      if (!message || message.trim().length === 0) {
        throw new ApiServiceError('Message cannot be empty', undefined, 'VALIDATION_ERROR');
      }

      if (!model || model.trim().length === 0) {
        throw new ApiServiceError('Model name cannot be empty', undefined, 'VALIDATION_ERROR');
      }

      // Optional health check before making the request
      if (performHealthCheck) {
        const isHealthy = await this.healthCheck();
        if (!isHealthy) {
          throw new ApiServiceError(
            'AI service is currently unavailable',
            undefined,
            'SERVICE_UNAVAILABLE'
          );
        }
      }

      // Prepare the request
      const request: GenerateRequest = {
        model: model.trim(),
        prompt: message.trim(),
        stream: false,
        ...(systemPrompt && { system: systemPrompt.trim() }),
        options: {
          temperature: 0.7,
          top_p: 0.9,
        },
      };

      // Make the request using existing retry logic
      const response = await this.generate(request);

      // Validate response
      if (!response.response || response.response.trim().length === 0) {
        throw new ApiServiceError(
          'Received empty response from AI',
          undefined,
          'EMPTY_RESPONSE'
        );
      }

      return response.response.trim();

    } catch (error) {
      // Enhanced error handling with user-friendly messages
      if (error instanceof ApiServiceError) {
        // Log the original error for debugging
        console.error('[sendMessage] API Error:', {
          message: error.message,
          status: error.status,
          code: error.code,
          model,
          messageLength: message.length,
        });

        // Return user-friendly error messages based on error type
        switch (error.code) {
          case 'VALIDATION_ERROR':
            throw new Error(error.message);

          case 'SERVICE_UNAVAILABLE':
            throw new Error('The AI service is currently unavailable. Please try again later.');

          case 'NETWORK_ERROR':
            throw new Error('Unable to connect to the AI service. Please check your internet connection.');

          case 'HTTP_ERROR':
            if (error.status === 404) {
              throw new Error(`The AI model "${model}" is not available. Please check the model name.`);
            } else if (error.status === 429) {
              throw new Error('Too many requests. Please wait a moment and try again.');
            } else if (error.status === 500) {
              throw new Error('The AI service is experiencing technical difficulties. Please try again.');
            } else if (error.status && error.status >= 400 && error.status < 500) {
              throw new Error('Invalid request. Please check your input and try again.');
            } else {
              throw new Error('The AI service is temporarily unavailable. Please try again.');
            }

          case 'EMPTY_RESPONSE':
            throw new Error('The AI did not provide a response. Please try rephrasing your message.');

          default:
            throw new Error('An unexpected error occurred while processing your message. Please try again.');
        }
      } else {
        // Handle non-API errors
        console.error('[sendMessage] Unexpected Error:', error);
        throw new Error('An unexpected error occurred. Please try again.');
      }
    }
  }
}

// Create and export singleton instance
export const apiService = new ApiService();

/**
 * Standalone sendMessage function for easy import and use in React Native components
 * Uses the singleton apiService instance with all production-ready features
 *
 * @param message - The user's message/prompt
 * @param model - The AI model to use (defaults to 'deepseek-r1:14b')
 * @param systemPrompt - Optional system prompt to guide the AI's behavior
 * @param performHealthCheck - Whether to check API health before sending (defaults to true)
 * @returns Promise<string> - The AI's response text
 *
 * @example
 * ```typescript
 * import { sendMessage } from './services/api';
 *
 * try {
 *   const response = await sendMessage('Hello, how are you?');
 *   console.log(response);
 * } catch (error) {
 *   console.error('Chat error:', error.message);
 * }
 * ```
 */
export async function sendMessage(
  message: string,
  model: string = 'deepseek-r1:14b',
  systemPrompt?: string,
  performHealthCheck: boolean = true
): Promise<string> {
  return apiService.sendMessage(message, model, systemPrompt, performHealthCheck);
}

// Export the class for testing or custom instances
export default ApiService;
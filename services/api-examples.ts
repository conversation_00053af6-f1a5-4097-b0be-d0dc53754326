/**
 * Example usage of the production-ready Axios API service
 * This file demonstrates how to use the apiService for various scenarios
 */

import { apiService, GenerateRequest, ApiServiceError } from './api';

/**
 * Example 1: Basic prompt generation
 * Equivalent to: curl http://127.0.0.1:11434/api/generate -d '{"model": "deepseek-r1:14b", "prompt": "Why is the sky blue?"}'
 */
export async function basicPromptExample(): Promise<void> {
  try {
    const response = await apiService.sendPrompt(
      'deepseek-r1:14b',
      'Why is the sky blue?'
    );
    
    console.log('AI Response:', response);
  } catch (error) {
    if (error instanceof ApiServiceError) {
      console.error('API Error:', {
        message: error.message,
        status: error.status,
        code: error.code,
      });
    } else {
      console.error('Unexpected error:', error);
    }
  }
}

/**
 * Example 2: Advanced generation with options
 */
export async function advancedGenerationExample(): Promise<void> {
  try {
    const request: GenerateRequest = {
      model: 'deepseek-r1:14b',
      prompt: 'Explain quantum computing in simple terms',
      stream: false,
      options: {
        temperature: 0.7,
        top_p: 0.9,
        top_k: 40,
        repeat_penalty: 1.1,
        num_predict: 500,
      },
      system: 'You are a helpful AI assistant that explains complex topics clearly.',
    };

    const response = await apiService.generate(request);
    
    console.log('Generated Response:', {
      text: response.response,
      model: response.model,
      duration: response.total_duration,
      tokenCount: response.eval_count,
    });
  } catch (error) {
    console.error('Generation failed:', error);
  }
}

/**
 * Example 3: Health check before making requests
 */
export async function healthCheckExample(): Promise<boolean> {
  try {
    const isHealthy = await apiService.healthCheck();
    
    if (isHealthy) {
      console.log('✅ API is healthy and ready');
      return true;
    } else {
      console.warn('⚠️ API health check failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Health check error:', error);
    return false;
  }
}

/**
 * Example 4: Batch processing with error handling
 */
export async function batchProcessingExample(): Promise<void> {
  const prompts = [
    'What is machine learning?',
    'Explain neural networks',
    'What is deep learning?',
  ];

  const results = await Promise.allSettled(
    prompts.map(async (prompt, index) => {
      try {
        const response = await apiService.sendPrompt('deepseek-r1:14b', prompt);
        return { index, prompt, response, success: true };
      } catch (error) {
        return { 
          index, 
          prompt, 
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false 
        };
      }
    })
  );

  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const data = result.value;
      if (data.success) {
        console.log(`✅ Prompt ${index + 1} succeeded:`, data.response.substring(0, 100) + '...');
      } else {
        console.error(`❌ Prompt ${index + 1} failed:`, data.error);
      }
    } else {
      console.error(`❌ Prompt ${index + 1} rejected:`, result.reason);
    }
  });
}

/**
 * Example 5: Custom request with full control
 */
export async function customRequestExample(): Promise<void> {
  try {
    // Example: Get model information
    const modelInfo = await apiService.customRequest({
      method: 'POST',
      url: '/api/show',
      data: { name: 'deepseek-r1:14b' },
      timeout: 10000,
    });

    console.log('Model Info:', modelInfo);
  } catch (error) {
    console.error('Custom request failed:', error);
  }
}

/**
 * Example 6: Configuration management
 */
export function configurationExample(): void {
  // Get current configuration
  const config = apiService.getConfig();
  console.log('Current API Configuration:', config);

  // Update retry configuration
  apiService.updateRetryConfig({
    attempts: 5,
    delay: 2000,
    backoff: true,
  });

  console.log('Updated configuration:', apiService.getConfig());
}

/**
 * Example 7: Error handling patterns
 */
export async function errorHandlingExample(): Promise<void> {
  try {
    // This will likely fail if the model doesn't exist
    await apiService.sendPrompt('non-existent-model', 'Hello');
  } catch (error) {
    if (error instanceof ApiServiceError) {
      switch (error.code) {
        case 'HTTP_ERROR':
          if (error.status === 404) {
            console.error('Model not found. Please check the model name.');
          } else if (error.status === 500) {
            console.error('Server error. Please try again later.');
          } else {
            console.error(`HTTP Error ${error.status}: ${error.message}`);
          }
          break;
        
        case 'NETWORK_ERROR':
          console.error('Network error. Please check your connection and server status.');
          break;
        
        case 'UNKNOWN_ERROR':
        default:
          console.error('An unexpected error occurred:', error.message);
          break;
      }
    } else {
      console.error('Non-API error:', error);
    }
  }
}

/**
 * Example 8: Production usage pattern
 */
export class ChatService {
  private model: string;

  constructor(model: string = 'deepseek-r1:14b') {
    this.model = model;
  }

  async sendMessage(message: string, systemPrompt?: string): Promise<string> {
    // Health check before processing
    const isHealthy = await apiService.healthCheck();
    if (!isHealthy) {
      throw new Error('API service is not available');
    }

    try {
      const request: GenerateRequest = {
        model: this.model,
        prompt: message,
        stream: false,
        system: systemPrompt,
        options: {
          temperature: 0.7,
          top_p: 0.9,
        },
      };

      const response = await apiService.generate(request);
      return response.response;
    } catch (error) {
      // Log error for monitoring
      console.error('[ChatService] Message failed:', {
        message,
        model: this.model,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Re-throw with user-friendly message
      throw new Error('Failed to process your message. Please try again.');
    }
  }

  async isModelAvailable(): Promise<boolean> {
    try {
      await apiService.customRequest({
        method: 'POST',
        url: '/api/show',
        data: { name: this.model },
        timeout: 5000,
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export a default chat service instance
export const defaultChatService = new ChatService();
